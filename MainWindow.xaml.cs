using PC_Control2.Demo.ViewModels;
using PC_Control2.Demo.Models;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Effects;
using System.Threading.Tasks;
using System;

namespace PC_Control2.Demo;

public partial class MainWindow : Window
{
    public MainWindowViewModel? ViewModel { get; private set; }
    private Point _dragStartPoint;
    private DateTime _lastDragTime = DateTime.MinValue;
    private bool _preventMenuOpen = false;
    private bool _isDragging = false;

    // 工具箱拖拽相关
    private bool _isToolboxDragging = false;
    private Point _toolboxDragStartPoint;
    private ToolboxNodeViewModel? _draggingToolboxNode;
    private Window? _dragPreviewWindow;
    private System.Windows.Threading.DispatcherTimer? _previewTrackingTimer;

    // 无参构造函数供XAML使用
    public MainWindow()
    {
        InitializeComponent();

        // 设置窗口图标和标题
        Title = "工业PC图形化设计系统 - Demo";

        // 订阅关闭事件
        Closing += MainWindow_Closing;
    }

    // 带参数的构造函数供代码使用
    public MainWindow(MainWindowViewModel viewModel) : this()
    {
        ViewModel = viewModel;
        DataContext = ViewModel;
    }

    private void MainWindow_Closing(object? sender, System.ComponentModel.CancelEventArgs e)
    {
        // 检查是否有未保存的更改
        if (ViewModel?.CurrentProject != null)
        {
            var result = MessageBox.Show(
                "是否保存当前项目的更改？",
                "保存确认",
                MessageBoxButton.YesNoCancel,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Cancel)
            {
                e.Cancel = true;
                return;
            }

            if (result == MessageBoxResult.Yes)
            {
                // 这里应该调用保存逻辑
                // await ViewModel.SaveProjectAsync();
            }
        }

        // 清理资源
        _previewTrackingTimer?.Stop();
        _dragPreviewWindow?.Close();
    }

    private void MainWindow_Loaded(object sender, RoutedEventArgs e)
    {
        // 窗口加载完成后的初始化
        if (ViewModel != null)
        {
            // 可以在这里执行一些需要UI完全加载后才能进行的操作
        }
    }

    // 标题栏拖拽相关事件处理
    private void TitleBar_MouseDown(object sender, MouseButtonEventArgs e)
    {
        System.Diagnostics.Debug.WriteLine($"TitleBar_MouseDown called: {e.OriginalSource?.GetType().Name}");
        
        if (e.ChangedButton == MouseButton.Left)
        {
            // 检查是否点击在菜单项上
            var hitTest = e.OriginalSource as DependencyObject;

            // 使用VisualTreeHelper遍历可视化树，检查是否点击在MenuItem上
            while (hitTest != null)
            {
                if (hitTest is MenuItem)
                {
                    // 如果点击在MenuItem上，不进行拖拽，让菜单正常工作
                    return;
                }

                // 检查是否是MenuItem的内部元素（如TextBlock、Border等）
                if (hitTest is FrameworkElement element)
                {
                    // 检查DataContext是否指向MenuItem
                    if (element.DataContext is MenuItem)
                    {
                        return;
                    }

                    // 检查是否是MenuItem模板的一部分
                    var templatedParent = element.TemplatedParent;
                    if (templatedParent is MenuItem)
                    {
                        return;
                    }
                }

                // 向上遍历可视化树
                hitTest = VisualTreeHelper.GetParent(hitTest);
            }

            // 如果没有点击在MenuItem上，进行窗口拖拽
            if (e.ClickCount == 2)
            {
                // 双击最大化/还原
                if (WindowState == WindowState.Maximized)
                    WindowState = WindowState.Normal;
                else
                    WindowState = WindowState.Maximized;
            }
            else
            {
                // 单击拖拽
                try
                {
                    DragMove();
                }
                catch (InvalidOperationException)
                {
                    // 忽略拖拽时可能出现的异常
                }
            }
        }
    }

    private void TitleBar_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
    {
        if (e.ChangedButton == MouseButton.Left)
        {
            // 记录拖拽起始点
            _dragStartPoint = e.GetPosition(this);
            _isDragging = false;

            // 检查是否点击在菜单项上
            var hitTest = e.OriginalSource as DependencyObject;
            bool isMenuItemClick = false;

            // 使用VisualTreeHelper遍历可视化树，检查是否点击在MenuItem上
            while (hitTest != null)
            {
                if (hitTest is MenuItem)
                {
                    System.Diagnostics.Debug.WriteLine("Clicked on MenuItem - allowing normal menu behavior");
                    isMenuItemClick = true;
                    break;
                }

                // 检查是否是MenuItem的内部元素
                if (hitTest is FrameworkElement element)
                {
                    // 检查是否是MenuItem模板的一部分
                    var templatedParent = element.TemplatedParent;
                    if (templatedParent is MenuItem)
                    {
                        System.Diagnostics.Debug.WriteLine("Clicked on MenuItem template part - allowing normal menu behavior");
                        isMenuItemClick = true;
                        break;
                    }
                }

                // 向上遍历可视化树
                hitTest = VisualTreeHelper.GetParent(hitTest);
            }

            // 如果点击在菜单项上，直接返回，不处理拖拽
            if (isMenuItemClick)
            {
                return;
            }

            // 如果不是菜单项，处理窗口拖拽
            if (e.ClickCount == 2)
            {
                // 双击最大化/还原
                if (WindowState == WindowState.Maximized)
                    WindowState = WindowState.Normal;
                else
                    WindowState = WindowState.Maximized;
            }
            else
            {
                // 单击开始拖拽
                try
                {
                    DragMove();
                }
                catch (InvalidOperationException)
                {
                    // 忽略拖拽时可能出现的异常
                }
            }
        }
    }

    // 窗口控制按钮事件
    private void MinimizeButton_Click(object sender, RoutedEventArgs e)
    {
        WindowState = WindowState.Minimized;
    }

    private void MaximizeButton_Click(object sender, RoutedEventArgs e)
    {
        if (WindowState == WindowState.Maximized)
            WindowState = WindowState.Normal;
        else
            WindowState = WindowState.Maximized;
    }

    private void CloseButton_Click(object sender, RoutedEventArgs e)
    {
        Close();
    }

    // 菜单事件处理
    private void NewProject_Click(object sender, RoutedEventArgs e)
    {
        // TODO: 实现新建项目功能
        MessageBox.Show("新建项目功能待实现", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private void OpenProject_Click(object sender, RoutedEventArgs e)
    {
        // TODO: 实现打开项目功能
        MessageBox.Show("打开项目功能待实现", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private void SaveProject_Click(object sender, RoutedEventArgs e)
    {
        // TODO: 实现保存项目功能
        MessageBox.Show("保存项目功能待实现", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private void SaveAsProject_Click(object sender, RoutedEventArgs e)
    {
        // TODO: 实现另存为项目功能
        MessageBox.Show("另存为项目功能待实现", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private void Exit_Click(object sender, RoutedEventArgs e)
    {
        Close();
    }

    // 工具箱拖拽事件处理
    private void ToolboxItem_MouseDown(object sender, MouseButtonEventArgs e)
    {
        if (e.LeftButton == MouseButtonState.Pressed)
        {
            _isToolboxDragging = true;
            _toolboxDragStartPoint = e.GetPosition((IInputElement)sender);
            
            if (sender is FrameworkElement element && element.DataContext is ToolboxNodeViewModel nodeViewModel)
            {
                _draggingToolboxNode = nodeViewModel;
            }
        }
    }

    private void ToolboxItem_MouseMove(object sender, MouseEventArgs e)
    {
        if (_isToolboxDragging && e.LeftButton == MouseButtonState.Pressed && _draggingToolboxNode != null)
        {
            var currentPosition = e.GetPosition((IInputElement)sender);
            var diff = currentPosition - _toolboxDragStartPoint;

            if (Math.Abs(diff.X) > SystemParameters.MinimumHorizontalDragDistance ||
                Math.Abs(diff.Y) > SystemParameters.MinimumVerticalDragDistance)
            {
                StartToolboxDrag();
            }
        }
    }

    private void ToolboxItem_MouseUp(object sender, MouseButtonEventArgs e)
    {
        EndToolboxDrag();
    }

    private void StartToolboxDrag()
    {
        if (_draggingToolboxNode == null) return;

        // 创建拖拽预览窗口
        CreateDragPreviewWindow();

        // 开始跟踪鼠标位置
        StartPreviewTracking();

        // 执行拖拽操作
        var data = new DataObject("ToolboxNode", _draggingToolboxNode);
        DragDrop.DoDragDrop(this, data, DragDropEffects.Copy);

        // 清理
        EndToolboxDrag();
    }

    private void CreateDragPreviewWindow()
    {
        if (_draggingToolboxNode == null) return;

        _dragPreviewWindow = new Window
        {
            WindowStyle = WindowStyle.None,
            AllowsTransparency = true,
            Background = System.Windows.Media.Brushes.Transparent,
            ShowInTaskbar = false,
            Topmost = true,
            Width = 100,
            Height = 60
        };

        var border = new Border
        {
            Background = new SolidColorBrush(Colors.LightBlue),
            BorderBrush = new SolidColorBrush(Colors.Blue),
            BorderThickness = new Thickness(1),
            CornerRadius = new CornerRadius(3),
            Opacity = 0.8
        };

        var textBlock = new TextBlock
        {
            Text = _draggingToolboxNode.Name,
            HorizontalAlignment = HorizontalAlignment.Center,
            VerticalAlignment = VerticalAlignment.Center,
            Foreground = new SolidColorBrush(Colors.Black),
            FontSize = 10
        };

        border.Child = textBlock;
        _dragPreviewWindow.Content = border;
    }

    private void StartPreviewTracking()
    {
        _previewTrackingTimer = new System.Windows.Threading.DispatcherTimer
        {
            Interval = TimeSpan.FromMilliseconds(16) // ~60 FPS
        };

        _previewTrackingTimer.Tick += (s, e) =>
        {
            if (_dragPreviewWindow != null)
            {
                var mousePos = System.Windows.Forms.Control.MousePosition;
                _dragPreviewWindow.Left = mousePos.X + 10;
                _dragPreviewWindow.Top = mousePos.Y + 10;

                if (!_dragPreviewWindow.IsVisible)
                {
                    _dragPreviewWindow.Show();
                }
            }
        };

        _previewTrackingTimer.Start();
    }

    private void EndToolboxDrag()
    {
        _isToolboxDragging = false;
        _draggingToolboxNode = null;

        _previewTrackingTimer?.Stop();
        _previewTrackingTimer = null;

        _dragPreviewWindow?.Close();
        _dragPreviewWindow = null;
    }

    // 拖拽操作已移至UEBlueprintEditorView

    // SFC编辑器事件处理已移至SFCEditorView



    // XAML中引用的事件处理方法
    private void Window_Loaded(object sender, RoutedEventArgs e)
    {
        MainWindow_Loaded(sender, e);
    }

    private void Menu_PreviewMouseDown(object sender, MouseButtonEventArgs e)
    {
        // 菜单预览鼠标按下事件
    }

    private void Menu_PreviewMouseUp(object sender, MouseButtonEventArgs e)
    {
        // 菜单预览鼠标释放事件
    }

    private void Menu_MouseDown(object sender, MouseButtonEventArgs e)
    {
        // 菜单鼠标按下事件
    }

    private void Menu_MouseUp(object sender, MouseButtonEventArgs e)
    {
        // 菜单鼠标释放事件
    }

    private void MenuItem_PreviewMouseDown(object sender, MouseButtonEventArgs e)
    {
        // 菜单项预览鼠标按下事件
    }

    private void MenuItem_Click(object sender, RoutedEventArgs e)
    {
        // 菜单项点击事件
    }

    private void MenuItem_MouseDown(object sender, MouseButtonEventArgs e)
    {
        // 菜单项鼠标按下事件
    }

    private void MenuItem_MouseUp(object sender, MouseButtonEventArgs e)
    {
        // 菜单项鼠标释放事件
    }

    private void MenuItem_PreviewMouseUp(object sender, MouseButtonEventArgs e)
    {
        // 菜单项预览鼠标释放事件
    }



    private void TreeView_SelectedItemChanged(object sender, RoutedPropertyChangedEventArgs<object> e)
    {
        // 树视图选择项变更事件
    }

    private void TreeViewItem_MouseRightButtonDown(object sender, MouseButtonEventArgs e)
    {
        // 树视图项右键按下事件
    }

    private void TreeViewItem_MouseDoubleClick(object sender, MouseButtonEventArgs e)
    {
        // 树视图项双击事件
    }

    private void ToolboxNode_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
    {
        ToolboxItem_MouseDown(sender, e);
    }

    private void ToolboxNode_MouseMove(object sender, MouseEventArgs e)
    {
        ToolboxItem_MouseMove(sender, e);
    }

    private void ToolboxNode_MouseLeftButtonUp(object sender, MouseButtonEventArgs e)
    {
        ToolboxItem_MouseUp(sender, e);
    }

}
