using Microsoft.Extensions.Logging;
using PC_Control2.Demo.Services;
using PC_Control2.Demo.ViewModels;
using System.Windows;

namespace PC_Control2.Demo;

public partial class App : Application
{
    protected override void OnStartup(StartupEventArgs e)
    {
        base.OnStartup(e);

        // 添加全局异常处理
        AppDomain.CurrentDomain.UnhandledException += OnUnhandledException;
        DispatcherUnhandledException += OnDispatcherUnhandledException;

        try
        {
            // 创建服务
            var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
            var projectLogger = loggerFactory.CreateLogger<ProjectService>();
            var blueprintLogger = loggerFactory.CreateLogger<BlueprintService>();
            var nodeDiscoveryLogger = loggerFactory.CreateLogger<NodeDiscoveryService>();
            var mainWindowLogger = loggerFactory.CreateLogger<MainWindowViewModel>();

            // 创建服务实例
            var nodeDiscoveryService = new NodeDiscoveryService(nodeDiscoveryLogger);
            var projectService = new ProjectService(projectLogger);
            var blueprintService = new BlueprintService(blueprintLogger, nodeDiscoveryService);

            // SFC节点库会通过特性自动发现，无需手动注册

            // 创建ViewModels
            var logViewModel = new LogViewModel();
            var toolboxViewModel = new ToolboxViewModel(nodeDiscoveryService);
            var projectTreeViewModel = new ProjectTreeViewModel();
            var blueprintCanvasViewModel = new BlueprintCanvasViewModel(blueprintService);
            var blueprintEditorViewModel = new NodeNetworkBlueprintViewModel(blueprintService, nodeDiscoveryService);

            // 先创建主窗口ViewModel（不包含SFC编辑器）
            var mainWindowViewModel = new MainWindowViewModel(
                projectService,
                projectTreeViewModel,
                blueprintCanvasViewModel,
                blueprintEditorViewModel,
                toolboxViewModel,
                logViewModel,
                mainWindowLogger);

            // 创建增强版SFC编辑器
            var sfcEditorViewModel = new EnhancedSFCViewModel();

            // 设置SFC编辑器到主窗口ViewModel
            mainWindowViewModel.SetSFCEditor(sfcEditorViewModel);

            // 创建主窗口
            var mainWindow = new MainWindow(mainWindowViewModel);
            mainWindow.Show();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"应用程序启动失败: {ex.Message}\n\n{ex.StackTrace}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void OnUnhandledException(object sender, UnhandledExceptionEventArgs e)
    {
        var ex = e.ExceptionObject as Exception;
        MessageBox.Show($"未处理的异常: {ex?.Message}\n\n{ex?.StackTrace}", "严重错误", MessageBoxButton.OK, MessageBoxImage.Error);
    }

    private void OnDispatcherUnhandledException(object sender, System.Windows.Threading.DispatcherUnhandledExceptionEventArgs e)
    {
        MessageBox.Show($"UI线程异常: {e.Exception.Message}\n\n{e.Exception.StackTrace}", "UI错误", MessageBoxButton.OK, MessageBoxImage.Error);
        e.Handled = true;
    }
}
