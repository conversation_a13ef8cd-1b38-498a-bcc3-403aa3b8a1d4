@echo off
chcp 65001 > nul
echo ========================================
echo 工业PC图形化设计系统 Demo 构建脚本
echo ========================================

echo.
echo 正在检查.NET 8.0 SDK...
dotnet --version
if %errorlevel% neq 0 (
    echo 错误: 未找到.NET 8.0 SDK，请先安装.NET 8.0 SDK
    pause
    exit /b 1
)

echo.
echo 正在还原NuGet包...
dotnet restore PC_Control2.Demo.csproj
if %errorlevel% neq 0 (
    echo 错误: NuGet包还原失败
    pause
    exit /b 1
)

echo.
echo 正在构建项目...
dotnet build PC_Control2.Demo.csproj --configuration Release
if %errorlevel% neq 0 (
    echo 错误: 项目构建失败
    pause
    exit /b 1
)

echo.
echo ========================================
echo 构建成功！
echo ========================================
echo.
echo 运行Demo程序请执行: .\run.bat
echo.
pause
